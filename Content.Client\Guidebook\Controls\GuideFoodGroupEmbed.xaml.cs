using System.Diagnostics.CodeAnalysis;
using System.Linq;
using Content.Client.Guidebook.Richtext;
using Content.Client.Nutrition.EntitySystems;
using JetBrains.Annotations;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Guidebook.Controls;

[UsedImplicitly, GenerateTypedNameReferences]
public sealed partial class GuideFoodGroupEmbed : BoxContainer, IDocumentTag
{
    [Dependency] private readonly IEntitySystemManager _sysMan = default!;

    public GuideFoodGroupEmbed()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        MouseFilter = MouseFilterMode.Stop;

        foreach (var data in _sysMan.GetEntitySystem<FoodGuideDataSystem>().Registry.OrderBy(it => it.Identifier))
        {
            var embed = new GuideFoodEmbed(data);
            GroupContainer.AddChild(embed);
        }
    }

    public bool TryParseTag(Dictionary<string, string> args, [NotNullWhen(true)] out Control? control)
    {
        control = this;
        return true;
    }
}
