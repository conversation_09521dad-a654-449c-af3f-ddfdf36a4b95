<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <!-- Work around https://github.com/dotnet/project-system/issues/4314 -->
    <TargetFramework>$(TargetFramework)</TargetFramework>
    <LangVersion>12</LangVersion>
    <IsPackable>false</IsPackable>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputPath>..\bin\Content.Client\</OutputPath>
    <OutputType Condition="'$(FullRelease)' != 'True'">Exe</OutputType>
    <WarningsAsErrors>nullable</WarningsAsErrors>
    <Nullable>enable</Nullable>
    <Configurations>Debug;Release;Tools;DebugOpt</Configurations>
    <Platforms>AnyCPU</Platforms>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Nett" />
    <PackageReference Include="JetBrains.Annotations" PrivateAssets="All" />
    <PackageReference Include="MathNet.Numerics" PrivateAssets="All" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RobustToolbox\Lidgren.Network\Lidgren.Network.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared.Maths\Robust.Shared.Maths.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Client\Robust.Client.csproj" />
    <ProjectReference Include="..\Content.Shared\Content.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Spawners\" />
    <Folder Include="_Goobstation\Devil\UI\" />
    <Folder Include="_EE\Shadowling\UI\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Lobby\UI\LobbyCharacterPanel.xaml.cs">
      <DependentUpon>LobbyCharacterPanel.xaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
  <Import Project="..\RobustToolbox\MSBuild\XamlIL.targets" />
  <ItemGroup>
    <EmbeddedResource Update="_Goobstation\Heretic\UI\HereticRitualRuneRadialMenu.xaml">
      <Generator>MSBuild:Compile</Generator>
    </EmbeddedResource>
  </ItemGroup>
</Project>
