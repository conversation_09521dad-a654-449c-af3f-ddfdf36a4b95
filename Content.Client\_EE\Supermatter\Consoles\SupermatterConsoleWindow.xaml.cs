using Content.Client.Message;
using Content.Client.UserInterface.Controls;
using Content.Shared._EE.Supermatter.Components;
using Content.Shared._EE.Supermatter.Monitor;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;
using Robust.Shared.Utility;
using System.Diagnostics.CodeAnalysis;
using System.Linq;

namespace Content.Client._EE.Supermatter.Consoles;

[GenerateTypedNameReferences]
public sealed partial class SupermatterConsoleWindow : FancyWindow
{
    private readonly IEntityManager _entManager;

    private EntityUid? _owner;
    private NetEntity? _trackedEntity;

    private SupermatterConsoleEntry[]? _supermatters = null;

    public event Action<NetEntity?>? SendFocusChangeMessageAction;

    private bool _autoScrollActive = false;
    private bool _autoScrollAwaitsUpdate = false;

    public SupermatterConsoleWindow(SupermatterConsoleBoundUserInterface userInterface, EntityUid? owner)
    {
        RobustXamlLoader.Load(this);
        _entManager = IoCManager.Resolve<IEntityManager>();
        // Pass the owner to nav map
        _owner = owner;

        // Set supermatter monitoring message action
        SendFocusChangeMessageAction += userInterface.SendFocusChangeMessage;
    }

    public void UpdateUI(SupermatterConsoleEntry[] supermatters, SupermatterFocusData? focusData)
    {
        if (_owner == null)
            return;

        if (!_entManager.TryGetComponent<SupermatterConsoleComponent>(_owner.Value, out var console))
            return;

        if (_trackedEntity != focusData?.NetEntity)
        {
            SendFocusChangeMessageAction?.Invoke(_trackedEntity);
            focusData = null;
        }

        // Retain supermatter data for use inbetween updates
        _supermatters = supermatters;

        // Clear excess children from the tables
        var supermattersCount = _supermatters.Count();

        while (SupermattersTable.ChildCount > supermattersCount)
            SupermattersTable.RemoveChild(SupermattersTable.GetChild(SupermattersTable.ChildCount - 1));

        // Update all entries in each table
        for (int index = 0; index < _supermatters.Count(); index++)
        {
            var entry = _supermatters.ElementAt(index);
            UpdateUIEntry(entry, index, SupermattersTable, console, focusData);
        }

        // If no alerts are active, display a message
        if (supermattersCount == 0)
        {
            var label = new RichTextLabel()
            {
                HorizontalExpand = true,
                VerticalExpand = true,
                HorizontalAlignment = HAlignment.Center,
                VerticalAlignment = VAlignment.Center,
            };

            label.SetMarkup(Loc.GetString("supermatter-console-window-no-supermatters"));

            SupermattersTable.AddChild(label);
        }

        // Auto-scroll re-enable
        if (_autoScrollAwaitsUpdate)
        {
            _autoScrollActive = true;
            _autoScrollAwaitsUpdate = false;
        }
    }

    private void UpdateUIEntry(SupermatterConsoleEntry entry, int index, Control table, SupermatterConsoleComponent console, SupermatterFocusData? focusData = null)
    {
        // Make new UI entry if required
        if (index >= table.ChildCount)
        {
            var newEntryContainer = new SupermatterEntryContainer(entry.NetEntity);

            // On click
            newEntryContainer.FocusButton.OnButtonUp += args =>
            {
                if (_trackedEntity == newEntryContainer.NetEntity)
                {
                    _trackedEntity = null;
                }

                else
                {
                    _trackedEntity = newEntryContainer.NetEntity;
                }

                // Send message to console that the focus has changed
                SendFocusChangeMessageAction?.Invoke(_trackedEntity);
            };

            // Add the entry to the current table
            table.AddChild(newEntryContainer);
        }

        // Update values and UI elements
        var tableChild = table.GetChild(index);

        if (tableChild is not SupermatterEntryContainer)
        {
            table.RemoveChild(tableChild);
            UpdateUIEntry(entry, index, table, console, focusData);

            return;
        }

        var entryContainer = (SupermatterEntryContainer) tableChild;

        entryContainer.UpdateEntry(entry, entry.NetEntity == _trackedEntity, focusData);
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        AutoScrollToFocus();
    }

    private void ActivateAutoScrollToFocus()
    {
        _autoScrollActive = false;
        _autoScrollAwaitsUpdate = true;
    }

    private void AutoScrollToFocus()
    {
        if (!_autoScrollActive)
            return;

        var scroll = SupermattersTable.Parent as ScrollContainer;
        if (scroll == null)
            return;

        if (!TryGetVerticalScrollbar(scroll, out var vScrollbar))
            return;

        if (!TryGetNextScrollPosition(out float? nextScrollPosition))
            return;

        vScrollbar.ValueTarget = nextScrollPosition.Value;

        if (MathHelper.CloseToPercent(vScrollbar.Value, vScrollbar.ValueTarget))
            _autoScrollActive = false;
    }

    private bool TryGetVerticalScrollbar(ScrollContainer scroll, [NotNullWhen(true)] out VScrollBar? vScrollBar)
    {
        vScrollBar = null;

        foreach (var child in scroll.Children)
        {
            if (child is not VScrollBar)
                continue;

            var castChild = child as VScrollBar;

            if (castChild != null)
            {
                vScrollBar = castChild;
                return true;
            }
        }

        return false;
    }

    private bool TryGetNextScrollPosition([NotNullWhen(true)] out float? nextScrollPosition)
    {
        nextScrollPosition = null;

        var scroll = SupermattersTable.Parent as ScrollContainer;
        if (scroll == null)
            return false;

        var container = scroll.Children.ElementAt(0) as BoxContainer;
        if (container == null || container.Children.Count() == 0)
            return false;

        // Exit if the heights of the children haven't been initialized yet
        if (!container.Children.Any(x => x.Height > 0))
            return false;

        nextScrollPosition = 0;

        foreach (var control in container.Children)
        {
            if (control == null || control is not SupermatterEntryContainer)
                continue;

            if (((SupermatterEntryContainer) control).NetEntity == _trackedEntity)
                return true;

            nextScrollPosition += control.Height;
        }

        // Failed to find control
        nextScrollPosition = null;

        return false;
    }

    private SupermatterStatusType GetStatus(NetEntity netEntity)
    {
        var status = _supermatters?.FirstOrNull(x => x.NetEntity == netEntity)?.EntityStatus;

        if (status == null)
            return SupermatterStatusType.Error;

        return status.Value;
    }
}
