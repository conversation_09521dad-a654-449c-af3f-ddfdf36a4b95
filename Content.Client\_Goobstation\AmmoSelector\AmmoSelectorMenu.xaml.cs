using Content.Client.UserInterface.Controls;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using System.Numerics;
using Content.Shared._Goobstation.Weapons.AmmoSelector;

namespace Content.Client._Goobstation.AmmoSelector;

[GenerateTypedNameReferences]
public sealed partial class AmmoSelectorMenu : RadialMenu
{
    [Dependency] private readonly EntityManager _entManager = default!;
    [Dependency] private readonly IPrototypeManager _protoManager = default!;

    private SpriteSystem _sprites;

    public event Action<ProtoId<SelectableAmmoPrototype>>? SendAmmoSelectorSystemMessageAction;

    private EntityUid _item;

    public AmmoSelectorMenu()
    {
        IoCManager.InjectDependencies(this);
        RobustXamlLoader.Load(this);
        _sprites = _entManager.System<SpriteSystem>();
    }

    public void SetEntity(EntityUid uid)
    {
        _item = uid;
        Refresh();
    }

    public void Refresh()
    {
        var main = FindControl<RadialContainer>("Main");
        main.RemoveAllChildren();

        if (!_entManager.TryGetComponent(_item, out AmmoSelectorComponent? ammoSelector))
            return;

        foreach (var ammo in ammoSelector.Prototypes)
        {
            if (!_protoManager.TryIndex(ammo, out var prototype))
                continue;

            var button = new AmmoSelectorMenuButton
            {
                StyleClasses = { "RadialMenuButton" },
                SetSize = new Vector2(64, 64),
                ToolTip = Loc.GetString(prototype.Desc),
                ProtoId = prototype.ID
            };

            var texture = new TextureRect
            {
                VerticalAlignment = VAlignment.Center,
                HorizontalAlignment = HAlignment.Center,
                Texture = _sprites.Frame0(prototype.Icon),
                TextureScale = new Vector2(2f, 2f)
            };

            button.AddChild(texture);
            main.AddChild(button);
        }

        AddAmmoSelectorMenuButtonOnClickActions(main);
    }

    private void AddAmmoSelectorMenuButtonOnClickActions(RadialContainer control)
    {
        foreach (var child in control.Children)
        {
            if (child is not AmmoSelectorMenuButton castChild)
                continue;

            castChild.OnButtonUp += _ =>
            {
                SendAmmoSelectorSystemMessageAction?.Invoke(castChild.ProtoId);
                Close();
            };
        }
    }
}

public sealed class AmmoSelectorMenuButton : RadialMenuTextureButton
{
    public ProtoId<SelectableAmmoPrototype> ProtoId { get; set; }
}
